"use client";
import { useForm } from "react-hook-form";
import {
  Mail,
  Phone,
  MapPin,
  Clock,
  Send,
  Facebook,
  Twitter,
  Instagram,
} from "lucide-react";
import <PERSON>ton from "../ui/button";
import { Input } from "../ui/input";

// ContactInfoCard component
const ContactInfoCard = ({ icon: Icon, title, details, color }) => {
  return (
    <div className="flex items-start space-x-4">
      <div className={`${color} p-3 rounded-full`}>
        <Icon className="h-6 w-6 text-white" />
      </div>
      <div>
        <h4 className="font-semibold text-gray-800 mb-1">{title}</h4>
        <p className="text-gray-600">{details}</p>
      </div>
    </div>
  );
};

const Contact = () => {

  const contactInfo = [
    {
      icon: MapPin,
      title: "Our Magical Location",
      details: "1234 Education Drive, Learning City, LC 12345",
      color: "bg-blue-500",
      emoji: "🏫",
      gradient: "kid-bg-gradient-1"
    },
    {
      icon: Phone,
      title: "Call Us Anytime",
      details: "(555) 123-LEARN",
      color: "bg-green-500",
      emoji: "📞",
      gradient: "kid-bg-gradient-2"
    },
    {
      icon: Mail,
      title: "Send Us a Message",
      details: "<EMAIL>",
      color: "bg-orange-500",
      emoji: "✉️",
      gradient: "kid-bg-gradient-3"
    },
    {
      icon: Clock,
      title: "Fun Learning Hours",
      details: "Mon - Fri: 7:30 AM - 6:00 PM",
      color: "bg-purple-500",
      emoji: "⏰",
      gradient: "kid-bg-gradient-4"
    },
  ];

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();

  const onSubmit = (data) => {
    console.log("Form submitted:", data);
    alert("Thank you for your message! We will get back to you soon.");
    reset();
  };

  return (
    <section id="contact" className="py-20 bg-gradient-to-br from-green-50 via-blue-50 to-purple-50 relative overflow-hidden">
      {/* Background Decorative Elements */}
      <div className="absolute top-10 right-10 w-24 h-24 bg-pink-200 rounded-full opacity-20 kid-float"></div>
      <div className="absolute bottom-20 left-10 w-16 h-16 bg-yellow-200 rounded-full opacity-20 kid-bounce"></div>
      <div className="absolute top-1/2 left-1/4 w-12 h-12 bg-blue-200 rounded-full opacity-20 kid-float" style={{animationDelay: '1s'}}></div>

      <div className="max-w-6xl mx-auto px-6">
        {/* Heading */}
        <div className="text-center mb-16 fade-in-up animate">
          <div className="text-6xl mb-4 kid-bounce">📞</div>
          <h2 className="kid-font-primary text-4xl md:text-6xl font-bold text-gray-800 mb-6 kid-scale-bounce">
            Let's Chat!
          </h2>
          <p className="kid-font-secondary text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto mb-6">
            🌟 We'd love to hear from you and answer all your questions! 🌟
          </p>
          <div className="flex justify-center items-center space-x-2 mb-4">
            <div className="w-8 h-2 kid-bg-gradient-1 rounded-full"></div>
            <div className="w-12 h-2 kid-bg-gradient-2 rounded-full"></div>
            <div className="w-8 h-2 kid-bg-gradient-3 rounded-full"></div>
          </div>
          <p className="kid-font-secondary text-lg text-gray-600">
            Reach out to learn about our amazing programs or schedule a fun visit! 🎉
          </p>
        </div>

        {/* Grid */}
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Left: Info + Social */}
          <div>
            <h3 className="text-2xl font-bold text-gray-800 mb-8">
              Get in Touch
            </h3>
            <div className="space-y-6 mb-8">
              {contactInfo.map((info, idx) => (
                <ContactInfoCard
                  key={idx}
                  icon={info.icon}
                  title={info.title}
                  details={info.details}
                  color={info.color}
                />
              ))}
            </div>

            {/* Social */}
            <h4 className="font-semibold text-gray-800 mb-4">
              Connect With Us
            </h4>
            <div className="flex space-x-4">
              <a
                href="#"
                className="bg-blue-100 hover:bg-blue-200 p-3 rounded-full transition"
              >
                <Facebook className="h-5 w-5 text-blue-600" />
              </a>
              <a
                href="#"
                className="bg-blue-100 hover:bg-blue-200 p-3 rounded-full transition"
              >
                <Twitter className="h-5 w-5 text-blue-600" />
              </a>
              <a
                href="#"
                className="bg-pink-100 hover:bg-pink-200 p-3 rounded-full transition"
              >
                <Instagram className="h-5 w-5 text-pink-600" />
              </a>
            </div>
          </div>

          {/* Right: Contact Form */}
          <div className="bg-gray-50 rounded-xl p-8">
            <h3 className="text-2xl font-bold text-gray-800 mb-6">
              Send us a Message
            </h3>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="name"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Full Name *
                  </label>
                  <Input
                    id="name"
                    placeholder="Your full name"
                    {...register("name", { required: "Full name is required" })}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="phone"
                    className="block text-sm font-medium text-gray-700 mb-2"
                  >
                    Phone Number
                  </label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="(*************"
                    {...register("phone")}
                  />
                </div>
              </div>

              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Email Address *
                </label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register("email", {
                    required: "Email is required",
                    pattern: {
                      value: /^\S+@\S+$/i,
                      message: "Enter a valid email address",
                    },
                  })}
                />
                {errors.email && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.email.message}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="message"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Message *
                </label>
                <textarea
                  id="message"
                  rows={4}
                  placeholder="Tell us about your child's needs or ask any questions..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  {...register("message", { required: "Message is required" })}
                />
                {errors.message && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.message.message}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-500 to-orange-500 hover:from-blue-600 hover:to-orange-600 text-white py-3 rounded-lg font-semibold transition-all duration-300 hover:shadow-lg"
              >
                <Send className="mr-2 h-5 w-5" />
                Send Message
              </Button>
            </form>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="mt-16 bg-gradient-to-r from-blue-500 to-orange-500 rounded-xl p-8 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">Stay Updated</h3>
          <p className="mb-6">
            Subscribe to our newsletter for updates on events and programs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input
              type="email"
              placeholder="Enter your email"
              className="flex-1 bg-white text-gray-800"
            />
            <Button>Subscribe</Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
// 
