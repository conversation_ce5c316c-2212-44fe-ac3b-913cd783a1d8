import React from 'react';
import { FileText, Calendar, Users, <PERSON>, <PERSON>rk<PERSON>, Heart } from 'lucide-react';
import Button from '../ui/button';

const Admissions = () => {
  const admissionSteps = [
    {
      icon: FileText,
      title: "Start Your Journey",
      description: "Fill out our fun application form and tell us all about your amazing little star! 📝✨",
      color: "blue",
      emoji: "📝",
      gradient: "kid-bg-gradient-1",
      step: "1"
    },
    {
      icon: Calendar,
      title: "Meet & Greet",
      description: "Come visit our magical school and let us get to know your wonderful child! 🤝🌟",
      color: "green",
      emoji: "🤝",
      gradient: "kid-bg-gradient-2",
      step: "2"
    },
    {
      icon: Users,
      title: "Welcome Home",
      description: "Join our amazing school family and start the most exciting learning adventure! 🎉🏠",
      color: "orange",
      emoji: "🎉",
      gradient: "kid-bg-gradient-3",
      step: "3"
    }
  ];



  return (
    <section id="admissions" className="py-20 bg-gradient-to-br from-green-50 via-yellow-50 to-orange-50 relative overflow-hidden">
      {/* Background Decorative Elements */}
      <div className="absolute top-10 right-10 w-24 h-24 bg-pink-200 rounded-full opacity-20 kid-float"></div>
      <div className="absolute bottom-20 left-10 w-16 h-16 bg-blue-200 rounded-full opacity-20 kid-bounce"></div>
      <div className="absolute top-1/2 right-1/4 w-12 h-12 bg-yellow-200 rounded-full opacity-20 kid-float" style={{animationDelay: '1s'}}></div>

      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16 fade-in-up animate">
          <div className="inline-flex items-center space-x-3 mb-6">
            <Star className="w-8 h-8 text-green-500 kid-bounce" />
            <Sparkles className="w-6 h-6 text-yellow-500 kid-wiggle" />
            <Heart className="w-8 h-8 text-pink-500 kid-bounce" style={{animationDelay: '0.5s'}} />
          </div>

          <h2 className="kid-font-primary text-4xl md:text-6xl font-bold text-gray-800 mb-6 kid-scale-bounce">
            Join Our Family!
          </h2>

          <p className="kid-font-secondary text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto mb-6">
            🌟 Ready to start the most amazing learning adventure? Let's make magic happen together! 🌟
          </p>

          <div className="flex justify-center items-center space-x-2 mb-4">
            <div className="w-8 h-2 kid-bg-gradient-1 rounded-full"></div>
            <div className="w-12 h-2 kid-bg-gradient-2 rounded-full"></div>
            <div className="w-8 h-2 kid-bg-gradient-3 rounded-full"></div>
          </div>

          <p className="kid-font-secondary text-lg text-gray-600">
            Our enrollment process is as fun and easy as playing your favorite game! 🎮
          </p>
        </div>

        {/* Step-by-step Process */}
        <div className="grid md:grid-cols-3 gap-8 mb-12 fade-in-up animate">
          {admissionSteps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={index} className="relative">
                <div className={`kid-card ${step.gradient} text-white kid-shadow-hover relative overflow-hidden`}
                     style={{animationDelay: `${index * 0.3}s`}}>

                  {/* Step number */}
                  <div className="absolute top-3 left-3 bg-white/20 w-8 h-8 rounded-full flex items-center justify-center">
                    <span className="kid-font-primary text-lg font-bold">{step.step}</span>
                  </div>

                  {/* Floating emoji */}
                  <div className="absolute top-3 right-3 text-3xl kid-bounce" style={{animationDelay: `${index * 0.4}s`}}>
                    {step.emoji}
                  </div>

                  {/* Icon container */}
                  <div className="bg-white/20 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 kid-wiggle">
                    <Icon className="h-10 w-10 text-white" />
                  </div>

                  <div className="text-center">
                    <h3 className="kid-font-primary text-2xl font-bold mb-4">
                      {step.title}
                    </h3>
                    <p className="kid-font-secondary text-lg leading-relaxed">
                      {step.description}
                    </p>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-1">
                    <div className="w-2 h-2 bg-white/30 rounded-full kid-bounce"></div>
                    <div className="w-2 h-2 bg-white/30 rounded-full kid-bounce" style={{animationDelay: '0.2s'}}></div>
                    <div className="w-2 h-2 bg-white/30 rounded-full kid-bounce" style={{animationDelay: '0.4s'}}></div>
                  </div>
                </div>

                {/* Fun Connection Arrow */}
                {index < admissionSteps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                    <div className="text-4xl kid-bounce" style={{animationDelay: `${index * 0.5}s`}}>➡️</div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Call to Action */}
        <div className="fade-in-up animate">
          <div className="kid-card kid-bg-gradient-4 text-white p-8 text-center">
            <div className="text-6xl mb-4 kid-bounce">🎉</div>
            <h3 className="kid-font-primary text-3xl md:text-4xl font-bold mb-4">
              Ready for the Adventure?
            </h3>
            <p className="kid-font-secondary text-xl mb-8 max-w-3xl mx-auto">
              Join our amazing school family and give your child the most wonderful learning experience!
              We can't wait to meet your little superstar! ✨
            </p>

            {/* Fun Benefits */}
            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white/20 rounded-2xl p-4 kid-wiggle">
                <div className="text-3xl mb-2">🌟</div>
                <p className="kid-font-primary font-bold">Open All Year</p>
                <p className="kid-font-secondary text-sm">Join us anytime!</p>
              </div>
              <div className="bg-white/20 rounded-2xl p-4 kid-wiggle" style={{animationDelay: '0.2s'}}>
                <div className="text-3xl mb-2">💝</div>
                <p className="kid-font-primary font-bold">Financial Help</p>
                <p className="kid-font-secondary text-sm">We make it affordable!</p>
              </div>
              <div className="bg-white/20 rounded-2xl p-4 kid-wiggle" style={{animationDelay: '0.4s'}}>
                <div className="text-3xl mb-2">⏰</div>
                <p className="kid-font-primary font-bold">Flexible Times</p>
                <p className="kid-font-secondary text-sm">Perfect for your family!</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button className="kid-button bg-white text-purple-600 hover:bg-yellow-100 hover:scale-110 transform transition-all duration-300 shadow-2xl text-lg px-8 py-4">
                <Star className="mr-2 h-5 w-5 kid-bounce" />
                Start Application
              </Button>
              <Button className="kid-button bg-yellow-400 text-purple-600 hover:bg-yellow-300 hover:scale-110 transform transition-all duration-300 shadow-2xl text-lg px-8 py-4">
                <Heart className="mr-2 h-5 w-5 kid-bounce" />
                Schedule Tour
              </Button>
            </div>

            {/* Fun decorative elements */}
            <div className="flex justify-center space-x-4 mt-8">
              <span className="text-3xl kid-bounce">🎨</span>
              <span className="text-3xl kid-bounce" style={{animationDelay: '0.2s'}}>📚</span>
              <span className="text-3xl kid-bounce" style={{animationDelay: '0.4s'}}>🎵</span>
              <span className="text-3xl kid-bounce" style={{animationDelay: '0.6s'}}>🌈</span>
              <span className="text-3xl kid-bounce" style={{animationDelay: '0.8s'}}>⭐</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Admissions;