const Button = ({
  children,
  onClick,
  type = 'button',
  variant = 'primary',
  className = '',
  disabled = false,
}) => {
  const baseStyle =
    'kid-font-primary px-6 py-3 rounded-full font-bold transition-all duration-300 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl';

  const variants = {
    primary: 'kid-bg-gradient-1 text-white hover:shadow-pink-200',
    secondary: 'kid-bg-gradient-2 text-white hover:shadow-blue-200',
    success: 'kid-bg-gradient-3 text-white hover:shadow-orange-200',
    danger: 'bg-red-500 hover:bg-red-600 text-white hover:shadow-red-200',
    outline: 'bg-white border-2 border-pink-400 text-pink-600 hover:bg-pink-50 hover:border-pink-500',
  };

  const selectedVariant = variants[variant] || variants['primary'];

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`${baseStyle} ${selectedVariant} ${className} ${disabled ? 'opacity-50 cursor-not-allowed transform-none' : 'kid-scale-bounce'}`}
    >
      {children}
    </button>
  );
};

export default Button;
