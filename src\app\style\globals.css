@import "tailwindcss";
@import "./config.css";

/* Kid-Friendly Design System */

/* Custom Fonts for Playful Look */
@import url('https://fonts.googleapis.com/css2?family=Fredoka:wght@300;400;500;600;700&family=Comic+Neue:wght@300;400;700&display=swap');

/* Custom CSS Variables for Kid-Friendly Colors */
:root {
  /* Primary Kid-Friendly Colors */
  --kid-primary: #FF6B6B;
  --kid-secondary: #4ECDC4;
  --kid-accent: #FFE66D;
  --kid-purple: #A8E6CF;
  --kid-orange: #FFB347;
  --kid-pink: #FFB6C1;
  --kid-blue: #87CEEB;
  --kid-green: #98FB98;

  /* Gradient Combinations */
  --kid-gradient-1: linear-gradient(135deg, #FF6B6B, #FFE66D);
  --kid-gradient-2: linear-gradient(135deg, #4ECDC4, #A8E6CF);
  --kid-gradient-3: linear-gradient(135deg, #FFB347, #FFB6C1);
  --kid-gradient-4: linear-gradient(135deg, #87CEEB, #98FB98);

  /* Shadow and Effects */
  --kid-shadow: 0 8px 32px rgba(255, 107, 107, 0.2);
  --kid-shadow-hover: 0 12px 40px rgba(255, 107, 107, 0.3);
}

/* Global Animations */
@keyframes bounce-gentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

@keyframes pulse-color {
  0%, 100% {
    background-color: var(--kid-primary);
  }
  25% {
    background-color: var(--kid-secondary);
  }
  50% {
    background-color: var(--kid-accent);
  }
  75% {
    background-color: var(--kid-purple);
  }
}

@keyframes rainbow-text {
  0% { color: #FF6B6B; }
  16% { color: #FFB347; }
  32% { color: #FFE66D; }
  48% { color: #98FB98; }
  64% { color: #87CEEB; }
  80% { color: #A8E6CF; }
  100% { color: #FFB6C1; }
}

@keyframes scale-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Utility Classes for Kid-Friendly Design */
.kid-font-primary {
  font-family: 'Fredoka', cursive;
}

.kid-font-secondary {
  font-family: 'Comic Neue', cursive;
}

.kid-bounce {
  animation: bounce-gentle 2s infinite;
}

.kid-float {
  animation: float 3s ease-in-out infinite;
}

.kid-wiggle {
  animation: wiggle 2s ease-in-out;
}

.kid-wiggle:hover {
  animation: wiggle 0.5s ease-in-out;
}

.kid-pulse-color {
  animation: pulse-color 4s ease-in-out infinite;
}

.kid-rainbow-text {
  animation: rainbow-text 3s ease-in-out infinite;
}

.kid-scale-bounce:hover {
  animation: scale-bounce 0.3s ease-in-out;
}

/* Custom Gradient Backgrounds */
.kid-bg-gradient-1 {
  background: var(--kid-gradient-1);
}

.kid-bg-gradient-2 {
  background: var(--kid-gradient-2);
}

.kid-bg-gradient-3 {
  background: var(--kid-gradient-3);
}

.kid-bg-gradient-4 {
  background: var(--kid-gradient-4);
}

/* Playful Shadows */
.kid-shadow {
  box-shadow: var(--kid-shadow);
}

.kid-shadow-hover:hover {
  box-shadow: var(--kid-shadow-hover);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Rounded Corners for Kid-Friendly Look */
.kid-rounded {
  border-radius: 20px;
}

.kid-rounded-full {
  border-radius: 50px;
}

/* Playful Button Styles */
.kid-button {
  @apply px-6 py-3 rounded-full font-bold text-white transition-all duration-300 transform;
  font-family: 'Fredoka', cursive;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.kid-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.kid-button:active {
  transform: translateY(0) scale(0.98);
}

/* Playful Card Styles */
.kid-card {
  @apply bg-white rounded-3xl p-6 shadow-lg transition-all duration-300;
  border: 3px solid transparent;
}

.kid-card:hover {
  transform: translateY(-5px) rotate(1deg);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  border-color: var(--kid-primary);
}

/* Fun Loading Animation */
.kid-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid var(--kid-accent);
  border-radius: 50%;
  border-top-color: var(--kid-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Scroll Animations */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.fade-in-up.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Playful Form Styles */
.kid-input {
  @apply w-full px-4 py-3 rounded-2xl border-2 border-gray-200 focus:border-4 focus:outline-none transition-all duration-300;
  font-family: 'Comic Neue', cursive;
}

.kid-input:focus {
  border-color: var(--kid-primary);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  transform: scale(1.02);
}

/* Sparkle and floating animations */
@keyframes sparkle-animation {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: scale(0) rotate(360deg);
    opacity: 0;
  }
}

@keyframes float-up {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

/* Interactive hover effects */
.kid-interactive:hover {
  cursor: pointer;
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Magical glow effect */
.kid-glow {
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
  animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  from {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(255, 107, 107, 0.6);
  }
}

