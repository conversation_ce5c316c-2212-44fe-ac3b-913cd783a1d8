import React from 'react';
import { Clock, Users, Lightbulb, Heart, Gamepad2, <PERSON><PERSON><PERSON>, <PERSON>, Spark<PERSON>, <PERSON> } from 'lucide-react';

const Programs = () => {
  const programs = [
    {
      icon: Users,
      title: "Reading & Language Adventures",
      description: "Magical storytelling journeys where letters become friends and words come alive! 📚✨",
      age: "Ages 3-6",
      color: "blue",
      emoji: "📚",
      gradient: "kid-bg-gradient-1"
    },
    {
      icon: Calculator,
      title: "Math Magic",
      description: "Number games and counting adventures that make math feel like playing with toys! 🔢🎲",
      age: "Ages 3-6",
      color: "green",
      emoji: "🔢",
      gradient: "kid-bg-gradient-2"
    },
    {
      icon: Lightbulb,
      title: "STEAM & Creative Fun",
      description: "Amazing science experiments, art projects, and building adventures! 🔬🎨",
      age: "Ages 4-6",
      color: "purple",
      emoji: "🔬",
      gradient: "kid-bg-gradient-3"
    },
    {
      icon: Heart,
      title: "Friendship & Feelings",
      description: "Learning to be kind, share, and understand our emotions through fun activities! 💝🤗",
      age: "Ages 3-6",
      color: "pink",
      emoji: "💝",
      gradient: "kid-bg-gradient-4"
    },
    {
      icon: Gamepad2,
      title: "Super Communication",
      description: "Building amazing talking and listening superpowers with fun games! 🗣️🎯",
      age: "All Ages",
      color: "orange",
      emoji: "🗣️",
      gradient: "kid-bg-gradient-1"
    },
    {
      icon: Clock,
      title: "Problem-Solving Heroes",
      description: "Learning to solve problems and work together like real superheroes! 🦸‍♀️⚡",
      age: "Ages 4-6",
      color: "indigo",
      emoji: "🦸‍♀️",
      gradient: "kid-bg-gradient-2"
    }
  ];



  return (
    <section id="programs" className="py-20 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 relative overflow-hidden">
      {/* Background Decorative Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-yellow-200 rounded-full opacity-20 kid-float"></div>
      <div className="absolute bottom-20 right-10 w-16 h-16 bg-pink-200 rounded-full opacity-20 kid-bounce"></div>
      <div className="absolute top-1/2 left-1/4 w-12 h-12 bg-blue-200 rounded-full opacity-20 kid-float" style={{animationDelay: '1s'}}></div>

      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16 fade-in-up animate">
          <div className="inline-flex items-center space-x-3 mb-6">
            <Star className="w-8 h-8 text-purple-500 kid-bounce" />
            <Sparkles className="w-6 h-6 text-pink-500 kid-wiggle" />
            <Smile className="w-8 h-8 text-yellow-500 kid-bounce" style={{animationDelay: '0.5s'}} />
          </div>

          <h2 className="kid-font-primary text-4xl md:text-6xl font-bold text-gray-800 mb-6 kid-scale-bounce">
            Our Super Fun Programs
          </h2>

          <p className="kid-font-secondary text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto mb-6">
            🎉 Amazing adventures and learning experiences for every little superstar! 🎉
          </p>

          <div className="flex justify-center items-center space-x-2 mb-4">
            <div className="w-8 h-2 kid-bg-gradient-1 rounded-full"></div>
            <div className="w-12 h-2 kid-bg-gradient-2 rounded-full"></div>
            <div className="w-8 h-2 kid-bg-gradient-3 rounded-full"></div>
          </div>

          <p className="kid-font-secondary text-lg text-gray-600">
            Every program is designed to make learning feel like the best game ever! 🎮
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 fade-in-up animate">
          {programs.map((program, index) => {
            const Icon = program.icon;
            return (
              <div
                key={index}
                className="kid-card bg-white kid-shadow-hover relative overflow-hidden"
                style={{animationDelay: `${index * 0.2}s`}}
              >
                {/* Header with gradient background */}
                <div className={`${program.gradient} p-6 text-center text-white relative`}>
                  {/* Floating emoji */}
                  <div className="absolute top-2 right-2 text-2xl kid-bounce" style={{animationDelay: `${index * 0.3}s`}}>
                    {program.emoji}
                  </div>

                  {/* Icon container */}
                  <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 kid-wiggle">
                    <Icon className="h-8 w-8 text-white" />
                  </div>

                  {/* Age badge */}
                  <span className="bg-white/20 text-white px-4 py-2 rounded-full text-sm font-bold kid-font-primary">
                    {program.age}
                  </span>

                  {/* Decorative dots */}
                  <div className="absolute bottom-2 left-2 flex space-x-1">
                    <div className="w-2 h-2 bg-white/30 rounded-full kid-bounce"></div>
                    <div className="w-2 h-2 bg-white/30 rounded-full kid-bounce" style={{animationDelay: '0.2s'}}></div>
                    <div className="w-2 h-2 bg-white/30 rounded-full kid-bounce" style={{animationDelay: '0.4s'}}></div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="kid-font-primary text-2xl font-bold text-gray-800 mb-4 text-center">
                    {program.title}
                  </h3>
                  <p className="kid-font-secondary text-lg text-gray-600 leading-relaxed text-center">
                    {program.description}
                  </p>
                </div>

                {/* Fun bottom decoration */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-pink-300 via-yellow-300 to-blue-300"></div>
              </div>
            );
          })}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16 fade-in-up animate">
          <div className="kid-card kid-bg-gradient-4 text-white p-8 max-w-3xl mx-auto">
            <div className="text-5xl mb-4 kid-bounce">🎪</div>
            <h3 className="kid-font-primary text-3xl font-bold mb-4">Join the Fun!</h3>
            <p className="kid-font-secondary text-xl mb-6">
              Ready to start your child's amazing learning adventure? Let's make magic happen together!
            </p>
            <div className="flex justify-center space-x-3">
              <span className="text-2xl kid-bounce">🎨</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.2s'}}>📚</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.4s'}}>🔬</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.6s'}}>💝</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.8s'}}>🌟</span>
            </div>
          </div>
        </div>

        <div className="text-center mt-12">
          <p className="text-lg text-gray-700 mb-6">
            Ready to explore our programs in detail?
          </p>
          <button className="bg-gradient-to-r from-blue-500 to-orange-500 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300 hover:scale-105">
            Schedule a Visit
          </button>
        </div>
      </div>
    </section>
  );
};

export default Programs;