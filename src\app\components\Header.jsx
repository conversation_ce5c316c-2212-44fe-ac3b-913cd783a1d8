"use client";
import React, { useState } from 'react';
import { Menu, <PERSON>, <PERSON>, Sparkles } from 'lucide-react';
import Button from '../ui/button';
import Image from 'next/image';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const navigation = [
    { name: 'Home', href: '#home', icon: '🏠', color: 'text-pink-500' },
    { name: 'About Us', href: '#about', icon: '👨‍👩‍👧‍👦', color: 'text-blue-500' },
    { name: 'Curriculum', href: '#curriculum', icon: '📚', color: 'text-green-500' },
    { name: 'Programs', href: '#programs', icon: '🎨', color: 'text-purple-500' },
    { name: 'Admissions', href: '#admissions', icon: '🎓', color: 'text-orange-500' },
    { name: 'Contact', href: '#contact', icon: '📞', color: 'text-red-500' }
  ];

  return (
    <header className="fixed top-0 left-0 right-0 z-50 kid-bg-gradient-1 backdrop-blur-sm shadow-xl">
      {/* Floating decorative elements */}
      <div className="absolute top-2 left-10 w-4 h-4 bg-yellow-300 rounded-full kid-bounce opacity-70"></div>
      <div className="absolute top-4 right-20 w-3 h-3 bg-pink-300 rounded-full kid-float opacity-70"></div>
      <div className="absolute top-1 right-40 w-2 h-2 bg-blue-300 rounded-full kid-bounce opacity-70" style={{animationDelay: '0.5s'}}></div>

      <nav className="max-w-6xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center space-x-3 kid-wiggle">
            <div className="relative">
              <Image
                src="/media/logo.webp"
                alt="Learning Circle Logo"
                width={180}
                height={90}
                className="kid-scale-bounce"
              />
              <div className="absolute -top-2 -right-2">
                <Star className="w-6 h-6 text-yellow-400 kid-bounce" />
              </div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {navigation.map((item, index) => (
              <a
                key={item.name}
                href={item.href}
                className={`kid-font-primary text-white hover:text-yellow-200 font-bold transition-all duration-300 relative group px-3 py-2 rounded-full hover:bg-white/20 kid-scale-bounce ${item.color}`}
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <span className="text-lg mr-1">{item.icon}</span>
                {item.name}
                <span className="absolute -bottom-1 left-0 w-0 h-1 bg-yellow-300 rounded-full transition-all duration-300 group-hover:w-full"></span>
              </a>
            ))}
            <div className="relative">
              <Button className="kid-button kid-bg-gradient-3 hover:scale-110 transform transition-all duration-300 shadow-lg">
                <Sparkles className="w-4 h-4 mr-2" />
                Apply Now
              </Button>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full kid-bounce"></div>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-3 rounded-full text-white hover:bg-white/20 transition-all duration-300 kid-scale-bounce"
          >
            {isMenuOpen ? (
              <X className="h-6 w-6 kid-wiggle" />
            ) : (
              <Menu className="h-6 w-6 kid-bounce" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden mt-4 py-4 border-t border-white/30">
            <div className="flex flex-col space-y-4">
              {navigation.map((item, index) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="kid-font-primary text-white hover:text-yellow-200 font-bold py-3 px-4 rounded-2xl hover:bg-white/20 transition-all duration-300 kid-scale-bounce flex items-center"
                  onClick={() => setIsMenuOpen(false)}
                  style={{animationDelay: `${index * 0.1}s`}}
                >
                  <span className="text-xl mr-3">{item.icon}</span>
                  {item.name}
                </a>
              ))}
              <div className="pt-2">
                <Button className="w-full kid-button kid-bg-gradient-3 hover:scale-105 transform transition-all duration-300">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Apply Now
                </Button>
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
};

export default Header;