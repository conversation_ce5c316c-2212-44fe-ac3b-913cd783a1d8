import React from 'react';
import { <PERSON>, <PERSON>, Star, Target, Sparkles, BookOpen } from 'lucide-react';

const StoryMission = () => {
  return (
    <section id="about" className="py-20 bg-gradient-to-br from-yellow-50 via-pink-50 to-blue-50 relative overflow-hidden">
      {/* Background Decorative Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-pink-200 rounded-full opacity-30 kid-float"></div>
      <div className="absolute top-32 right-20 w-16 h-16 bg-blue-200 rounded-full opacity-30 kid-bounce"></div>
      <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-yellow-200 rounded-full opacity-30 kid-float" style={{animationDelay: '1s'}}></div>

      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16 fade-in-up animate">
          <div className="inline-flex items-center space-x-3 mb-6">
            <BookOpen className="w-8 h-8 text-pink-500 kid-bounce" />
            <Sparkles className="w-6 h-6 text-yellow-500 kid-wiggle" />
            <Heart className="w-8 h-8 text-red-500 kid-bounce" style={{animationDelay: '0.5s'}} />
          </div>

          <h2 className="kid-font-primary text-4xl md:text-6xl font-bold text-gray-800 mb-6 kid-scale-bounce">
            Our Story & Mission
          </h2>

          <div className="flex justify-center items-center space-x-2 mb-4">
            <div className="w-8 h-2 kid-bg-gradient-1 rounded-full"></div>
            <div className="w-12 h-2 kid-bg-gradient-2 rounded-full"></div>
            <div className="w-8 h-2 kid-bg-gradient-3 rounded-full"></div>
          </div>

          <p className="kid-font-secondary text-xl text-gray-600 max-w-2xl mx-auto">
            🌟 Every child is a shining star waiting to sparkle! 🌟
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div className="space-y-6 fade-in-up animate">
            <div className="kid-card kid-shadow-hover">
              <div className="flex items-start space-x-4">
                <div className="text-4xl">🏫</div>
                <div>
                  <h3 className="kid-font-primary text-xl font-bold text-gray-800 mb-3">Our Beginning</h3>
                  <p className="kid-font-secondary text-lg text-gray-700 leading-relaxed">
                    Our magical school began over 15 years ago with a dream to create a wonderland
                    where every child, regardless of their unique superpowers, can shine bright and reach for the stars! ✨
                  </p>
                </div>
              </div>
            </div>

            <div className="kid-card kid-shadow-hover">
              <div className="flex items-start space-x-4">
                <div className="text-4xl">👩‍🏫</div>
                <div>
                  <h3 className="kid-font-primary text-xl font-bold text-gray-800 mb-3">Our Super Teachers</h3>
                  <p className="kid-font-secondary text-lg text-gray-700 leading-relaxed">
                    Our amazing team of superhero educators are specialists who have created a
                    magical approach to learning that celebrates each child's special abilities! 🦸‍♀️
                  </p>
                </div>
              </div>
            </div>

            <div className="kid-card kid-shadow-hover">
              <div className="flex items-start space-x-4">
                <div className="text-4xl">🎯</div>
                <div>
                  <h3 className="kid-font-primary text-xl font-bold text-gray-800 mb-3">Our Mission</h3>
                  <p className="kid-font-secondary text-lg text-gray-700 leading-relaxed">
                    We believe early childhood is when little minds build their strongest foundations.
                    Learning should be fun, inclusive, and accessible through magical play-based adventures! 🎪
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="relative fade-in-up animate">
            <div className="grid grid-cols-2 gap-4">
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                  alt="Children learning together"
                  className="kid-rounded shadow-2xl hover:scale-105 transition-transform duration-300 kid-shadow-hover"
                />
                <div className="absolute -top-3 -left-3 text-3xl kid-bounce">🌟</div>
              </div>
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                  alt="Classroom environment"
                  className="kid-rounded shadow-2xl hover:scale-105 transition-transform duration-300 mt-8 kid-shadow-hover"
                />
                <div className="absolute -bottom-3 -right-3 text-3xl kid-float">🎨</div>
              </div>
            </div>
            <div className="absolute -bottom-6 -right-6 kid-bg-gradient-3 rounded-full p-4 kid-bounce">
              <Heart className="h-12 w-12 text-white" />
            </div>
            <div className="absolute -top-6 -left-6 bg-yellow-400 rounded-full p-3 kid-wiggle">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>

        {/* Mission Values */}
        <div className="grid md:grid-cols-3 gap-8 fade-in-up animate">
          <div className="kid-card text-center kid-bg-gradient-2 text-white kid-shadow-hover relative overflow-hidden">
            <div className="absolute top-2 right-2 text-2xl kid-bounce">🤗</div>
            <div className="bg-white/20 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 kid-wiggle">
              <Users className="h-10 w-10 text-white" />
            </div>
            <h3 className="kid-font-primary text-2xl font-bold mb-4">Inclusive Community</h3>
            <p className="kid-font-secondary text-lg leading-relaxed">
              Creating a magical rainbow where every child feels like a superstar! 🌈✨
            </p>
            <div className="absolute bottom-2 left-2 text-xl kid-float">💝</div>
          </div>

          <div className="kid-card text-center kid-bg-gradient-3 text-white kid-shadow-hover relative overflow-hidden">
            <div className="absolute top-2 right-2 text-2xl kid-bounce" style={{animationDelay: '0.5s'}}>🏆</div>
            <div className="bg-white/20 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 kid-wiggle">
              <Star className="h-10 w-10 text-white" />
            </div>
            <h3 className="kid-font-primary text-2xl font-bold mb-4">Excellence in Education</h3>
            <p className="kid-font-secondary text-lg leading-relaxed">
              Providing super-duper learning adventures tailored for each little genius! 🚀📚
            </p>
            <div className="absolute bottom-2 left-2 text-xl kid-float" style={{animationDelay: '0.3s'}}>⭐</div>
          </div>

          <div className="kid-card text-center kid-bg-gradient-4 text-white kid-shadow-hover relative overflow-hidden">
            <div className="absolute top-2 right-2 text-2xl kid-bounce" style={{animationDelay: '1s'}}>🎯</div>
            <div className="bg-white/20 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 kid-wiggle">
              <Target className="h-10 w-10 text-white" />
            </div>
            <h3 className="kid-font-primary text-2xl font-bold mb-4">Purposeful Growth</h3>
            <p className="kid-font-secondary text-lg leading-relaxed">
              Growing little minds through fun, meaningful learning adventures! 🌱🎪
            </p>
            <div className="absolute bottom-2 left-2 text-xl kid-float" style={{animationDelay: '0.7s'}}>🌟</div>
          </div>
        </div>

        {/* Fun Call to Action */}
        <div className="text-center mt-16 fade-in-up animate">
          <div className="kid-card kid-bg-gradient-1 text-white p-8 max-w-2xl mx-auto">
            <div className="text-6xl mb-4 kid-bounce">🎉</div>
            <h3 className="kid-font-primary text-3xl font-bold mb-4">Ready to Join Our Family?</h3>
            <p className="kid-font-secondary text-xl mb-6">
              Come discover the magic of learning where every day is an adventure!
            </p>
            <div className="flex justify-center space-x-2">
              <span className="text-2xl kid-bounce">🌟</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.2s'}}>🎨</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.4s'}}>📚</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.6s'}}>🎵</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.8s'}}>🌈</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StoryMission;