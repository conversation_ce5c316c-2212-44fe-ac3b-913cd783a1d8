import React from 'react';
import { Book<PERSON><PERSON>, Palette, <PERSON>, Brain, Music, Globe, Heart, Star, Sparkles } from 'lucide-react';

const Curriculum = () => {
  const curriculumItems = [
    {
      icon: Brain,
      title: "Multi-Literacy Learning",
      description: "Magical reading and writing adventures that make letters dance and words come alive! ✨📚",
      color: "blue",
      emoji: "🧠",
      gradient: "kid-bg-gradient-1"
    },
    {
      icon: Users,
      title: "Social-Emotional Approach",
      description: "Building friendship superpowers and kindness skills through fun group adventures! 🤝💝",
      color: "green",
      emoji: "👫",
      gradient: "kid-bg-gradient-2"
    },
    {
      icon: Palette,
      title: "Personalized Goals",
      description: "Every child gets their own special learning journey designed just for them! 🎯⭐",
      color: "orange",
      emoji: "🎨",
      gradient: "kid-bg-gradient-3"
    },
    {
      icon: BookOpen,
      title: "Reading & Language Arts",
      description: "Magical storytelling adventures where letters become friends and words come alive! 📖✨",
      color: "purple",
      emoji: "📚",
      gradient: "kid-bg-gradient-4"
    },
    {
      icon: Music,
      title: "Mathematics",
      description: "Number games and counting adventures that make math feel like playing! 🔢🎲",
      color: "pink",
      emoji: "🔢",
      gradient: "kid-bg-gradient-1"
    },
    {
      icon: Globe,
      title: "Science & Social Learning",
      description: "Exploring our amazing world through fun experiments and friendship building! 🌍🔬",
      color: "indigo",
      emoji: "🌍",
      gradient: "kid-bg-gradient-2"
    }
  ];



  return (
    <section id="curriculum" className="py-20 bg-gradient-to-br from-purple-50 via-pink-50 to-yellow-50 relative overflow-hidden">
      {/* Background Decorative Elements */}
      <div className="absolute top-10 right-10 w-24 h-24 bg-pink-200 rounded-full opacity-20 kid-float"></div>
      <div className="absolute bottom-20 left-10 w-16 h-16 bg-blue-200 rounded-full opacity-20 kid-bounce"></div>
      <div className="absolute top-1/2 right-1/4 w-12 h-12 bg-yellow-200 rounded-full opacity-20 kid-float" style={{animationDelay: '1s'}}></div>

      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16 fade-in-up animate">
          <div className="inline-flex items-center space-x-3 mb-6">
            <Star className="w-8 h-8 text-purple-500 kid-bounce" />
            <Sparkles className="w-6 h-6 text-pink-500 kid-wiggle" />
            <Heart className="w-8 h-8 text-red-500 kid-bounce" style={{animationDelay: '0.5s'}} />
          </div>

          <h2 className="kid-font-primary text-4xl md:text-6xl font-bold text-gray-800 mb-6 kid-scale-bounce">
            Our Magical Curriculum
          </h2>

          <p className="kid-font-secondary text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto mb-6">
            🌟 A super-fun learning adventure that helps every little star shine bright! 🌟
          </p>

          <div className="flex justify-center items-center space-x-2 mb-4">
            <div className="w-6 h-2 kid-bg-gradient-1 rounded-full"></div>
            <div className="w-10 h-2 kid-bg-gradient-2 rounded-full"></div>
            <div className="w-6 h-2 kid-bg-gradient-3 rounded-full"></div>
          </div>

          <p className="kid-font-secondary text-lg text-gray-600">
            Every day is filled with magical discoveries and joyful learning! ✨
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 fade-in-up animate">
          {curriculumItems.map((item, index) => {
            const Icon = item.icon;
            return (
              <div
                key={index}
                className={`kid-card ${item.gradient} text-white kid-shadow-hover relative overflow-hidden`}
                style={{animationDelay: `${index * 0.2}s`}}
              >
                {/* Floating emoji */}
                <div className="absolute top-3 right-3 text-3xl kid-bounce" style={{animationDelay: `${index * 0.3}s`}}>
                  {item.emoji}
                </div>

                {/* Icon container */}
                <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto kid-wiggle">
                  <Icon className="h-8 w-8 text-white" />
                </div>

                <h3 className="kid-font-primary text-2xl font-bold text-center mb-4">
                  {item.title}
                </h3>

                <p className="kid-font-secondary text-lg text-center leading-relaxed">
                  {item.description}
                </p>

                {/* Decorative elements */}
                <div className="absolute bottom-3 left-3 flex space-x-1">
                  <div className="w-2 h-2 bg-white/30 rounded-full kid-bounce"></div>
                  <div className="w-2 h-2 bg-white/30 rounded-full kid-bounce" style={{animationDelay: '0.2s'}}></div>
                  <div className="w-2 h-2 bg-white/30 rounded-full kid-bounce" style={{animationDelay: '0.4s'}}></div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Fun Call to Action */}
        <div className="text-center mt-16 fade-in-up animate">
          <div className="kid-card kid-bg-gradient-1 text-white p-8 max-w-3xl mx-auto">
            <div className="text-5xl mb-4 kid-bounce">🎓</div>
            <h3 className="kid-font-primary text-3xl font-bold mb-4">Ready for the Adventure?</h3>
            <p className="kid-font-secondary text-xl mb-6">
              Join us for an amazing learning journey where every child becomes a superstar!
            </p>
            <div className="flex justify-center space-x-3">
              <span className="text-2xl kid-bounce">📚</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.2s'}}>🎨</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.4s'}}>🔬</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.6s'}}>🎵</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.8s'}}>🌟</span>
            </div>
          </div>
        </div>

        <div className="text-center mt-12">
          <p className="text-lg text-gray-700 mb-6">
            Want to learn more about our teaching approach?
          </p>
          <button className="bg-gradient-to-r from-blue-500 to-orange-500 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300 hover:scale-105">
            Download Curriculum Guide
          </button>
        </div>
      </div>
    </section>
  );
};

export default Curriculum;