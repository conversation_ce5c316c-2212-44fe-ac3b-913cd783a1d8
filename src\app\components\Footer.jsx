import React from 'react';
import Image from 'next/image';
import { GraduationCap, Heart, Mail, Phone, MapPin, Star, Sparkles } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="kid-bg-gradient-1 text-white pt-12 relative overflow-hidden">
      {/* Background Decorative Elements */}
      <div className="absolute top-10 left-10 w-16 h-16 bg-white/10 rounded-full kid-float"></div>
      <div className="absolute bottom-20 right-10 w-12 h-12 bg-white/10 rounded-full kid-bounce"></div>
      <div className="absolute top-1/2 right-1/4 w-8 h-8 bg-white/10 rounded-full kid-float" style={{animationDelay: '1s'}}></div>

      <div className="max-w-6xl mx-auto px-6">
        <div className="grid md:grid-cols-4 gap-8 mb-8">
          {/* School Info */}
          <div className="col-span-2 fade-in-up animate">
            <div className="flex items-center space-x-3 mb-4 kid-wiggle">
              <Image src="/media/logo.webp" alt="Learning Circle Logo" width={180} height={90} />
              <div className="text-2xl kid-bounce">⭐</div>
            </div>
            <p className="kid-font-secondary text-lg text-white/90 mb-4 leading-relaxed">
              🌟 Nurturing every little star's potential through magical learning adventures,
              personalized growth, and a loving community where every child shines bright! ✨
            </p>
            <div className="flex space-x-3">
              <span className="text-2xl kid-bounce">🎨</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.2s'}}>📚</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.4s'}}>🎵</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.6s'}}>🌈</span>
            </div>
          </div>

          {/* Quick Links */}
          <div className="fade-in-up animate" style={{animationDelay: '0.2s'}}>
            <div className="flex items-center space-x-2 mb-4">
              <Star className="h-6 w-6 text-yellow-300 kid-bounce" />
              <h3 className="kid-font-primary font-bold text-xl">Quick Links</h3>
            </div>
            <ul className="space-y-3">
              <li><a href="#about" className="kid-font-secondary text-white/90 hover:text-yellow-300 transition-all duration-300 hover:scale-105 inline-block kid-scale-bounce">🏠 About Us</a></li>
              <li><a href="#curriculum" className="kid-font-secondary text-white/90 hover:text-yellow-300 transition-all duration-300 hover:scale-105 inline-block kid-scale-bounce">📚 Curriculum</a></li>
              <li><a href="#programs" className="kid-font-secondary text-white/90 hover:text-yellow-300 transition-all duration-300 hover:scale-105 inline-block kid-scale-bounce">🎨 Programs</a></li>
              <li><a href="#admissions" className="kid-font-secondary text-white/90 hover:text-yellow-300 transition-all duration-300 hover:scale-105 inline-block kid-scale-bounce">🎓 Admissions</a></li>
              <li><a href="#contact" className="kid-font-secondary text-white/90 hover:text-yellow-300 transition-all duration-300 hover:scale-105 inline-block kid-scale-bounce">📞 Contact</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="fade-in-up animate" style={{animationDelay: '0.4s'}}>
            <div className="flex items-center space-x-2 mb-4">
              <Sparkles className="h-6 w-6 text-yellow-300 kid-wiggle" />
              <h3 className="kid-font-primary font-bold text-xl">Contact Info</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-start space-x-3 bg-white/10 rounded-2xl p-3 kid-wiggle">
                <MapPin className="h-5 w-5 text-yellow-300 mt-1" />
                <span className="kid-font-secondary text-white/90 text-sm">🏫 1234 Education Drive<br />Learning City, LC 12345</span>
              </div>
              <div className="flex items-center space-x-3 bg-white/10 rounded-2xl p-3 kid-wiggle">
                <Phone className="h-5 w-5 text-yellow-300" />
                <span className="kid-font-secondary text-white/90 text-sm">📞 (555) 123-LEARN</span>
              </div>
              <div className="flex items-center space-x-3 bg-white/10 rounded-2xl p-3 kid-wiggle">
                <Mail className="h-5 w-5 text-yellow-300" />
                <span className="kid-font-secondary text-white/90 text-sm">✉️ <EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/20 py-6 fade-in-up animate" style={{animationDelay: '0.6s'}}>
          <div className="text-center mb-4">
            <div className="flex justify-center space-x-4 mb-4">
              <span className="text-3xl kid-bounce">🌟</span>
              <span className="text-3xl kid-bounce" style={{animationDelay: '0.2s'}}>💝</span>
              <span className="text-3xl kid-bounce" style={{animationDelay: '0.4s'}}>🎉</span>
              <span className="text-3xl kid-bounce" style={{animationDelay: '0.6s'}}>🌈</span>
              <span className="text-3xl kid-bounce" style={{animationDelay: '0.8s'}}>⭐</span>
            </div>
            <p className="kid-font-primary text-lg text-white/90 mb-2">
              Thank you for being part of our amazing family!
            </p>
            <p className="kid-font-secondary text-sm text-white/80">
              © {currentYear} Learning Circle. Made with 💝 for little superstars everywhere!
            </p>
          </div>

          <div className="flex flex-wrap justify-center space-x-6 mt-4">
            <a href="#" className="kid-font-secondary text-white/80 hover:text-yellow-300 text-sm transition-all duration-300 kid-scale-bounce">
              🔒 Privacy Policy
            </a>
            <a href="#" className="kid-font-secondary text-white/80 hover:text-yellow-300 text-sm transition-all duration-300 kid-scale-bounce">
              📋 Terms of Service
            </a>
            <a href="#" className="kid-font-secondary text-white/80 hover:text-yellow-300 text-sm transition-all duration-300 kid-scale-bounce">
              ♿ Accessibility
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;