"use client";
import React, { useState } from 'react';
import { ChevronLeft, ChevronR<PERSON>, Quo<PERSON>, <PERSON> } from 'lucide-react';

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON> of Emma (Age 5)",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
      quote: "I just told my child's school... '<PERSON> has flourished beyond my expectations. The individualized attention and inclusive environment have helped her gain confidence and develop strong foundational skills.'",
      rating: 5,
      highlight: "Gained confidence"
    },
    {
      name: "<PERSON>", 
      role: "Parent of <PERSON> (Age 4)",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
      quote: "The teachers here understand that every child learns differently. <PERSON> has made incredible progress in both social skills and academic readiness since joining.",
      rating: 5,
      highlight: "Incredible progress"
    },
    {
      name: "<PERSON>",
      role: "Parent of <PERSON> (Age 6)", 
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
      quote: "This school has been a blessing for our family. Sofia, who has special needs, receives the support she needs while being part of a loving, inclusive community.",
      rating: 5,
      highlight: "Loving community"
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const currentData = testimonials[currentTestimonial];

  return (
    <section className="py-20 bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 relative overflow-hidden">
      {/* Background Decorative Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-yellow-200 rounded-full opacity-20 kid-float"></div>
      <div className="absolute bottom-20 right-10 w-16 h-16 bg-pink-200 rounded-full opacity-20 kid-bounce"></div>

      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16 fade-in-up animate">
          <div className="text-6xl mb-4 kid-bounce">💝</div>
          <h2 className="kid-font-primary text-4xl md:text-6xl font-bold text-gray-800 mb-6 kid-scale-bounce">
            Happy Families Share
          </h2>
          <p className="kid-font-secondary text-xl md:text-2xl text-gray-700 max-w-3xl mx-auto">
            🌟 Hear the amazing stories from our wonderful school family! 🌟
          </p>
          <div className="flex justify-center items-center space-x-2 mt-6">
            <div className="w-8 h-2 kid-bg-gradient-1 rounded-full"></div>
            <div className="w-12 h-2 kid-bg-gradient-2 rounded-full"></div>
            <div className="w-8 h-2 kid-bg-gradient-3 rounded-full"></div>
          </div>
        </div>

        <div className="relative kid-card kid-bg-gradient-1 text-white p-8 md:p-12 max-w-4xl mx-auto kid-shadow-hover">
          {/* Quote Icon */}
          <div className="absolute top-6 left-6 bg-white/20 rounded-full p-3 kid-bounce">
            <Quote className="h-6 w-6 text-white" />
          </div>

          {/* Floating decorative elements */}
          <div className="absolute top-4 right-4 text-2xl kid-float">💫</div>
          <div className="absolute bottom-4 left-4 text-2xl kid-bounce">⭐</div>

          <div className="grid md:grid-cols-3 gap-8 items-center">
            {/* Testimonial Content */}
            <div className="md:col-span-2">
              <div className="flex items-center mb-4">
                {[...Array(currentData.rating)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              
              <blockquote className="kid-font-secondary text-lg md:text-xl leading-relaxed mb-6 italic">
                "{currentData.quote}"
              </blockquote>

              <div className="flex items-center space-x-4">
                <div>
                  <h4 className="kid-font-primary font-bold text-xl">{currentData.name}</h4>
                  <p className="kid-font-secondary text-lg opacity-90">{currentData.role}</p>
                  <div className="bg-white/20 text-white px-4 py-2 rounded-full text-sm mt-2 inline-block kid-wiggle">
                    ✨ {currentData.highlight} ✨
                  </div>
                </div>
              </div>
            </div>

            {/* Parent Image */}
            <div className="text-center">
              <img 
                src={currentData.image}
                alt={currentData.name}
                className="w-32 h-32 rounded-full mx-auto shadow-lg object-cover"
              />
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center mt-8">
            <button
              onClick={prevTestimonial}
              className="p-3 rounded-full bg-white/20 hover:bg-white/30 transition-all duration-300 kid-scale-bounce"
            >
              <ChevronLeft className="h-6 w-6 text-white" />
            </button>

            {/* Dots Indicator */}
            <div className="flex space-x-3">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-4 h-4 rounded-full transition-all duration-300 kid-bounce ${
                    index === currentTestimonial
                      ? 'bg-white scale-125'
                      : 'bg-white/40 hover:bg-white/60'
                  }`}
                  style={{animationDelay: `${index * 0.1}s`}}
                />
              ))}
            </div>

            <button
              onClick={nextTestimonial}
              className="p-3 rounded-full bg-white/20 hover:bg-white/30 transition-all duration-300 kid-scale-bounce"
            >
              <ChevronRight className="h-6 w-6 text-white" />
            </button>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12 fade-in-up animate">
          <div className="kid-card kid-bg-gradient-2 text-white p-8 max-w-2xl mx-auto">
            <div className="text-5xl mb-4 kid-bounce">🌟</div>
            <h3 className="kid-font-primary text-2xl md:text-3xl font-bold mb-4">
              Ready to Create Your Own Success Story?
            </h3>
            <p className="kid-font-secondary text-lg mb-6">
              Join our amazing family and watch your little star shine bright! ✨
            </p>
            <button className="kid-button bg-white text-purple-600 hover:bg-yellow-100 hover:scale-110 transform transition-all duration-300 shadow-2xl text-lg px-8 py-4">
              <Star className="mr-2 h-5 w-5 kid-bounce" />
              Schedule a Visit Today
            </button>
            <div className="flex justify-center space-x-3 mt-6">
              <span className="text-2xl kid-bounce">💝</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.2s'}}>🎉</span>
              <span className="text-2xl kid-bounce" style={{animationDelay: '0.4s'}}>🌈</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;