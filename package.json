{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "clsx": "^2.1.1", "framer-motion": "^12.20.0", "lucide-react": "^0.523.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.9", "@tailwindcss/typography": "^0.5.16", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4.1.9", "tailwindcss-animate": "^1.0.7"}}