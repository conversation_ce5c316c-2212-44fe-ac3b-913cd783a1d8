// Animation utilities for the kid-friendly preschool website

// Scroll-triggered animations
export const initScrollAnimations = () => {
  if (typeof window === 'undefined') return;

  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate');
      }
    });
  }, observerOptions);

  // Observe all elements with fade-in-up class
  const animatedElements = document.querySelectorAll('.fade-in-up');
  animatedElements.forEach((el) => observer.observe(el));
};

// Fun hover effects for interactive elements
export const addHoverEffects = () => {
  if (typeof window === 'undefined') return;

  // Add sparkle effect on hover for buttons
  const buttons = document.querySelectorAll('.kid-button');
  buttons.forEach(button => {
    button.addEventListener('mouseenter', createSparkles);
    button.addEventListener('mouseleave', removeSparkles);
  });
};

// Create sparkle effect
const createSparkles = (e) => {
  const button = e.target;
  const rect = button.getBoundingClientRect();
  
  for (let i = 0; i < 5; i++) {
    const sparkle = document.createElement('div');
    sparkle.className = 'sparkle';
    sparkle.style.cssText = `
      position: absolute;
      width: 4px;
      height: 4px;
      background: #FFD700;
      border-radius: 50%;
      pointer-events: none;
      animation: sparkle-animation 0.6s ease-out forwards;
      left: ${Math.random() * rect.width}px;
      top: ${Math.random() * rect.height}px;
    `;
    
    button.style.position = 'relative';
    button.appendChild(sparkle);
    
    setTimeout(() => sparkle.remove(), 600);
  }
};

// Remove sparkles
const removeSparkles = (e) => {
  const sparkles = e.target.querySelectorAll('.sparkle');
  sparkles.forEach(sparkle => sparkle.remove());
};

// Add floating animation to random elements
export const addFloatingElements = () => {
  if (typeof window === 'undefined') return;

  const floatingEmojis = ['⭐', '🌟', '✨', '💫', '🎈', '🎉', '🌈', '💝'];
  
  setInterval(() => {
    if (Math.random() > 0.7) { // 30% chance every interval
      createFloatingEmoji();
    }
  }, 3000);
};

const createFloatingEmoji = () => {
  const emoji = floatingEmojis[Math.floor(Math.random() * floatingEmojis.length)];
  const floatingElement = document.createElement('div');
  
  floatingElement.textContent = emoji;
  floatingElement.style.cssText = `
    position: fixed;
    font-size: 24px;
    pointer-events: none;
    z-index: 1000;
    left: ${Math.random() * window.innerWidth}px;
    top: ${window.innerHeight + 50}px;
    animation: float-up 4s ease-out forwards;
  `;
  
  document.body.appendChild(floatingElement);
  
  setTimeout(() => floatingElement.remove(), 4000);
};

// Initialize all animations
export const initAllAnimations = () => {
  initScrollAnimations();
  addHoverEffects();
  addFloatingElements();
};

// CSS for sparkle animation (to be added to global styles)
export const sparkleCSS = `
@keyframes sparkle-animation {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: scale(1) rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: scale(0) rotate(360deg);
    opacity: 0;
  }
}

@keyframes float-up {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}
`;
