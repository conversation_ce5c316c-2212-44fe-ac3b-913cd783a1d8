import React from 'react';
import { ArrowR<PERSON>, Play, Heart, <PERSON>, Sparkles, Sun, Cloud } from 'lucide-react';
import Button from '../ui/button';

const Hero = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center kid-bg-gradient-1 overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Floating Shapes */}
        <div className="absolute top-20 left-10 w-16 h-16 bg-yellow-300 rounded-full opacity-70 kid-float"></div>
        <div className="absolute top-40 right-20 w-12 h-12 bg-pink-300 rounded-full opacity-70 kid-bounce"></div>
        <div className="absolute bottom-32 left-20 w-20 h-20 bg-blue-300 rounded-full opacity-60 kid-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-60 left-1/4 w-8 h-8 bg-green-300 rounded-full opacity-70 kid-bounce" style={{animationDelay: '0.5s'}}></div>
        <div className="absolute bottom-40 right-1/4 w-14 h-14 bg-purple-300 rounded-full opacity-60 kid-float" style={{animationDelay: '2s'}}></div>

        {/* Animated Icons */}
        <div className="absolute top-32 right-10 kid-bounce">
          <Sun className="w-8 h-8 text-yellow-400 opacity-80" />
        </div>
        <div className="absolute top-80 left-32 kid-float">
          <Cloud className="w-10 h-10 text-white opacity-70" />
        </div>
        <div className="absolute bottom-60 right-32 kid-bounce" style={{animationDelay: '1.5s'}}>
          <Star className="w-6 h-6 text-yellow-300 opacity-80" />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center">
        <div className="fade-in-up animate">
          <div className="mb-8">
            <div className="inline-flex items-center space-x-2 mb-4">
              <Heart className="w-8 h-8 text-white kid-bounce" />
              <Sparkles className="w-6 h-6 text-yellow-300 kid-wiggle" />
              <Heart className="w-8 h-8 text-white kid-bounce" style={{animationDelay: '0.5s'}} />
            </div>
          </div>

          <h1 className="kid-font-primary text-5xl md:text-7xl font-bold text-white mb-6 leading-tight kid-scale-bounce">
            Welcome to
            <span className="kid-rainbow-text block mt-2">
              Learning Circle
            </span>
          </h1>

          <div className="relative inline-block mb-6">
            <p className="kid-font-secondary text-2xl md:text-3xl text-yellow-200 font-bold kid-wiggle">
              🌟 Supporting Every Child's Journey 🌟
            </p>
          </div>

          <p className="kid-font-secondary text-lg md:text-xl text-white mb-8 max-w-4xl mx-auto leading-relaxed bg-white/10 rounded-3xl p-6 backdrop-blur-sm">
            A magical space for social development, creativity, and early childhood education!
            Supporting children with learning differences, slow learners, and children with special needs.
            Our mission is to make quality education accessible, affordable, and joyful for every little star! ✨
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Button className='kid-button kid-bg-gradient-3 hover:scale-110 transform transition-all duration-300 shadow-2xl text-lg px-8 py-4'>
              <Star className="mr-2 h-5 w-5 kid-bounce" />
              Start Your Journey
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button className='kid-button bg-white text-pink-500 hover:bg-yellow-100 hover:scale-110 transform transition-all duration-300 shadow-2xl text-lg px-8 py-4'>
              <Play className="mr-2 h-5 w-5 kid-bounce" />
              Watch Our Story
            </Button>
          </div>
        </div>
      </div>

      {/* Additional Decorative Elements */}
      <div className="absolute bottom-10 left-10 flex space-x-2">
        <div className="w-4 h-4 bg-yellow-400 rounded-full kid-bounce"></div>
        <div className="w-4 h-4 bg-pink-400 rounded-full kid-bounce" style={{animationDelay: '0.2s'}}></div>
        <div className="w-4 h-4 bg-blue-400 rounded-full kid-bounce" style={{animationDelay: '0.4s'}}></div>
      </div>

      <div className="absolute bottom-10 right-10 flex space-x-2">
        <div className="w-3 h-3 bg-green-400 rounded-full kid-float"></div>
        <div className="w-3 h-3 bg-purple-400 rounded-full kid-float" style={{animationDelay: '0.3s'}}></div>
        <div className="w-3 h-3 bg-orange-400 rounded-full kid-float" style={{animationDelay: '0.6s'}}></div>
      </div>

      {/* Floating Emojis */}
      <div className="absolute top-1/4 left-8 text-4xl kid-float opacity-80">🎨</div>
      <div className="absolute top-1/3 right-8 text-3xl kid-bounce opacity-80">📚</div>
      <div className="absolute bottom-1/4 left-16 text-3xl kid-wiggle opacity-80">🎵</div>
      <div className="absolute bottom-1/3 right-16 text-4xl kid-float opacity-80">🌈</div>
    </section>
  );
};

export default Hero;
