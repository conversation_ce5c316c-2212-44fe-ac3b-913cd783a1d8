import React from 'react';
import { ArrowRight, Play } from 'lucide-react';
import Button from '../ui/button';

const Hero = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-pink-50 overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80')`
        }}
      />
      
      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center">
        <div className="animate-fade-in">
          <h1 className="text-5xl md:text-7xl font-bold text-gray-800 mb-6 leading-tight">
            Welcome to 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-500 via-pink-500 to-blue-500">
              {" "}Learning Circle
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-700 mb-4 font-medium">
            Supporting Education
          </p>
          <p className="text-lg text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            A nurturing space for social development, creativity, and early childhood education. 
            Supporting children with learning differences, slow learners, and children with special needs. 
            Our mission is to provide quality education accessible, affordable, and joyful.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button className='flex items-center ' variant='secondary'>
              Start Your Journey
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button 
             variant='outline'
              className='flex items-center'>
              <Play className="mr-2 h-5 w-5" />
              Watch Our Story
            </Button>
          </div>
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-green-200 rounded-full opacity-60 animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-16 h-16 bg-pink-200 rounded-full opacity-60 animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-4 w-12 h-12 bg-blue-200 rounded-full opacity-60 animate-pulse delay-500"></div>
    </section>
  );
};

export default Hero;
