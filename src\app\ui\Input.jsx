import * as React from "react";
import { cn } from "../lib/utils";

const Input = React.forwardRef(
  ({ className, type = "text", ...props }, ref) => {
    return (
      <input
        ref={ref}
        type={type}
        className={cn(
          "kid-font-secondary flex h-12 w-full rounded-2xl border-2 border-pink-200 bg-white px-4 py-3 text-base placeholder:text-gray-400 focus:outline-none focus:border-pink-400 focus:ring-4 focus:ring-pink-100 focus:scale-105 transition-all duration-300 disabled:cursor-not-allowed disabled:opacity-50 hover:border-pink-300 shadow-md focus:shadow-lg",
          className
        )}
        {...props}
      />
    );
  }
);

Input.displayName = "Input";

export { Input };
